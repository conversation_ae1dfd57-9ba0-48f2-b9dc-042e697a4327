import os
from dotenv import load_dotenv
import mysql.connector
import hashlib
import fitz  # PyMuPDF
import spacy
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# ========= LOAD .env =========
load_dotenv()

PDF_DIRECTORY = os.getenv("PDF_DIRECTORY")

MYSQL_HOST = os.getenv("MYSQL_HOST")
MYSQL_USER = os.getenv("MYSQL_USER")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD")
MYSQL_DATABASE = os.getenv("MYSQL_DATABASE")

if not PDF_DIRECTORY:
    raise ValueError("PDF_DIRECTORY is not set in .env file")

# Load spaCy once
try:
    nlp = spacy.load("en_core_web_sm")
except OSError:
    # Automatically download the model if missing
    import subprocess
    subprocess.run(["python", "-m", "spacy", "download", "en_core_web_sm"], check=True)
    nlp = spacy.load("en_core_web_sm")

# MySQL connection setup
conn = mysql.connector.connect(
    host=MYSQL_HOST,
    user=MYSQL_USER,
    password=MYSQL_PASSWORD,
    database=MYSQL_DATABASE
)
cursor = conn.cursor()

# ---------- Table Creation ----------
def ensure_pdf_chunks_table_exists():
    """Create pdf_chunks table if it does not exist."""
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS pdf_chunks (
            id INT AUTO_INCREMENT PRIMARY KEY,
            source_file VARCHAR(255),
            chunk_number INT,
            content LONGTEXT,
            file_hash VARCHAR(255),
            last_modified DATETIME,
            chunked BOOLEAN,
            vector_embedded BOOLEAN,
            page_number INT,
            created_at DATETIME
        )
    """)
    conn.commit()

# ---------- MySQL Helpers ----------
def fetch_existing_hashes():
    """Fetch file_hashes already processed and stored in the database."""
    cursor.execute("SELECT DISTINCT file_hash FROM pdf_chunks")
    return set(row[0] for row in cursor.fetchall())

def insert_chunks_to_db(chunks):
    """Insert new PDF chunks into the MySQL database."""
    for chunk in chunks:
        cursor.execute("""
            INSERT INTO pdf_chunks
            (source_file, chunk_number, content, file_hash, last_modified,
             chunked, vector_embedded, page_number, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            chunk['source_file'],
            int(chunk['chunk_number']),
            chunk['content'],
            chunk['file_hash'],
            chunk['last_modified'],
            chunk['status']['chunked'],
            chunk['status']['vector_embedded'],
            chunk['page_number'],
            chunk['created_at']
        ))
    conn.commit()

# ---------- Utility Functions ----------
def calculate_sha256_from_bytes(data):
    """Calculate SHA-256 from byte content."""
    return hashlib.sha256(data).hexdigest()

def extract_blocks_from_pdf(pdf_bytes):
    """Extract text blocks with page numbers."""
    pdf_document = fitz.open("pdf", pdf_bytes)
    results = []
    for page_number, page in enumerate(pdf_document, start=1):
        blocks = page.get_text("blocks")
        for block in blocks:
            text = block[4].strip()
            if not text:
                continue
            results.append({
                "page_number": page_number,
                "text": text
            })
    pdf_document.close()
    return results

def split_with_spacy(text):
    """Split text into sentences using spaCy."""
    doc = nlp(text)
    return [sent.text.strip() for sent in doc.sents if sent.text.strip()]

def regroup_sentences(sentences, max_words=250):
    """Regroup sentences into chunks of ~max_words words."""
    chunks, current_chunk, word_count = [], [], 0
    for sent in sentences:
        words = sent.split()
        if word_count + len(words) <= max_words:
            current_chunk.append(sent)
            word_count += len(words)
        else:
            chunks.append(" ".join(current_chunk))
            current_chunk = [sent]
            word_count = len(words)
    if current_chunk:
        chunks.append(" ".join(current_chunk))
    return chunks

# ---------- Main PDF Chunking Logic from Directory ----------
def process_all_pdfs_from_directory(pdf_dir):
    """Process all PDFs from a local directory."""
    ensure_pdf_chunks_table_exists()  # make sure table exists

    files = [f for f in os.listdir(pdf_dir) if f.lower().endswith(".pdf")]
    if not files:
        print("No PDF files found in directory.")
        return

    existing_hashes = fetch_existing_hashes()
    new_chunks = []

    def process_file(file_name):
        path = os.path.join(pdf_dir, file_name)
        with open(path, "rb") as f:
            file_data = f.read()
        file_hash = calculate_sha256_from_bytes(file_data)
        last_modified_str = datetime.now().isoformat()
        created_at_str = datetime.utcnow().isoformat()

        if file_hash in existing_hashes:
            print(f"Skipping {file_name} (already processed).")
            return []

        print(f"Processing {file_name}...")

        blocks = extract_blocks_from_pdf(file_data)
        if not blocks:
            return []

        processed_chunks = []
        for block in blocks:
            sentences = split_with_spacy(block["text"])
            sentence_chunks = regroup_sentences(sentences, max_words=250)

            for chunk_text in sentence_chunks:
                processed_chunks.append({
                    "content": chunk_text,
                    "page_number": block["page_number"]
                })

        return [{
            "source_file": file_name,
            "chunk_number": f"{idx+1}",
            "content": chunk["content"],
            "file_hash": file_hash,
            "last_modified": last_modified_str,
            "created_at": created_at_str,
            "page_number": chunk["page_number"],
            "status": {
                "chunked": True,
                "vector_embedded": False
            }
        } for idx, chunk in enumerate(processed_chunks)]

    # Threaded processing for speed
    with ThreadPoolExecutor() as executor:
        results = executor.map(process_file, files)
        for chunks in results:
            new_chunks.extend(chunks)

    if new_chunks:
        insert_chunks_to_db(new_chunks)
        print(f"Inserted {len(new_chunks)} new chunk(s) from directory.")
    else:
        print("No new chunks to insert from directory.")

# ---------- Main Entry Point ----------
if __name__ == "__main__":
    process_all_pdfs_from_directory(PDF_DIRECTORY)
